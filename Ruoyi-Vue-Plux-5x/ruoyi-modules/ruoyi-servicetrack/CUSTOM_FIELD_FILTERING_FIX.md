# Custom Field Filtering Enhancement

## 🎯 Enhancement Overview

Extended filtering support for custom fields, particularly **selection fields** (Dropdown, MultipleSelection, CheckBox, RadioBox) to properly handle `$contains` and `$notContains` operations with array values.

## 🔧 Key Improvements

### **1. Extended Operator Support for Selection Fields**

**Before**: `$contains` and `$notContains` only supported text fields
```java
case "$contains":
case "$notContains":
    // Only text fields support contains operations
    return fieldType.equals(1) || // ShortText
           fieldType.equals(2) || // PlainText
           fieldType.equals(6);   // RichText
```

**After**: Extended support to include selection fields
```java
case "$contains":
case "$notContains":
    // Text fields support LIKE operations, selection fields support IN operations
    return fieldType.equals(1) || // ShortText
           fieldType.equals(2) || // PlainText
           fieldType.equals(6) || // RichText
           fieldType.equals(3) || // Dropdown
           fieldType.equals(4) || // MultipleSelection
           fieldType.equals(7) || // CheckBox
           fieldType.equals(8);   // RadioBox
```

### **2. Smart Operator Interpretation**

**For Text Fields**: `$contains` → `LIKE '%value%'`
**For Selection Fields**: `$contains` → `IN (value1, value2, ...)`

### **3. Array Value Support**

**Input Formats Supported**:
- JSON Array: `[2,1,4]`
- String Array: `"[2,1,4]"`
- Single Value: `2`
- List Object: `[2, 1, 4]`

## 🔍 Technical Implementation

### **System Fields Enhancement**

**Status Field (fieldId=3)** now supports:
```java
// Input: {"fieldId":3,"operator":"$contains","value":[2,1,4]}
// Generated SQL: u.state_id IN (2, 1, 4)

private String buildInCondition(String columnName, Object value) {
    if (value.toString().startsWith("[") && value.toString().endsWith("]")) {
        // Handle array-like string: "[2,1,4]"
        String arrayStr = value.toString();
        String innerStr = arrayStr.substring(1, arrayStr.length() - 1);
        String[] values = innerStr.split(",");
        StringBuilder sb = new StringBuilder();
        sb.append(columnName).append(" IN (");
        for (int i = 0; i < values.length; i++) {
            if (i > 0) sb.append(", ");
            sb.append(values[i].trim());
        }
        sb.append(")");
        return sb.toString();
    }
    // ... handle other formats
}
```

### **Custom Selection Fields Enhancement**

**Dropdown/MultipleSelection Fields** now support:
```java
// Input: {"fieldId":10001,"operator":"$contains","value":[5,3,7]}
// Generated SQL: EXISTS (SELECT 1 FROM item_selection cf WHERE cf.project_id = u.project_id AND cf.item_id = u.item_id AND cf.field_id = 10001 AND cf.choice_id IN (5, 3, 7))

private String buildCustomFieldInCondition(String columnName, Object value) {
    if (value.toString().startsWith("[") && value.toString().endsWith("]")) {
        String arrayStr = value.toString();
        String innerStr = arrayStr.substring(1, arrayStr.length() - 1);
        String[] values = innerStr.split(",");
        StringBuilder sb = new StringBuilder();
        sb.append("cf.").append(columnName).append(" IN (");
        for (int i = 0; i < values.length; i++) {
            if (i > 0) sb.append(", ");
            sb.append(values[i].trim());
        }
        sb.append(")");
        return sb.toString();
    }
    // ... handle other formats
}
```

### **Field Type Detection**
```java
private boolean isSelectionField(Integer fieldType) {
    return fieldType != null && (
        fieldType.equals(3) || // Dropdown
        fieldType.equals(4) || // MultipleSelection
        fieldType.equals(7) || // CheckBox
        fieldType.equals(8)    // RadioBox
    );
}
```

## 📋 Supported Field Types & Operations

| Field Type | Table | Column | $eq/$ne | $contains/$notContains | Logic |
|------------|-------|--------|---------|------------------------|-------|
| **System Fields** |
| Title (1) | item_info | item_title | ✅ | ✅ LIKE | Text matching |
| Status (3) | item_info | state_id | ✅ | ✅ IN | Value list matching |
| Owner (4) | item_info | owner_id | ✅ | ✅ IN | Value list matching |
| **Custom Fields** |
| ShortText (1) | item_text | text | ✅ | ✅ LIKE | Text matching |
| PlainText (2) | item_text | text | ✅ | ✅ LIKE | Text matching |
| Dropdown (3) | item_selection | choice_id | ✅ | ✅ IN | Choice list matching |
| MultipleSelection (4) | item_selection | choice_id | ✅ | ✅ IN | Choice list matching |
| Date (5) | item_datetime | datetime | ✅ | ❌ | Date comparison only |
| CheckBox (7) | item_selection | choice_id | ✅ | ✅ IN | Choice list matching |
| RadioBox (8) | item_selection | choice_id | ✅ | ✅ IN | Choice list matching |
| Amount (11) | item_amount | amount | ✅ | ❌ | Numeric comparison only |

## 🧪 Test Cases

### **Test Case 1: System Field - Status IN**
```bash
GET /servicetrack/item/list?filterConditions=["[{\"fieldId\":3,\"operator\":\"$contains\",\"value\":[2,1,4]}]"]&projectId=101
```
**Expected SQL**: `u.state_id IN (2, 1, 4)`

### **Test Case 2: Custom Dropdown Field**
```bash
GET /servicetrack/item/list?filterConditions=["[{\"fieldId\":10001,\"operator\":\"$contains\",\"value\":[5,3]}]"]&projectId=101
```
**Expected SQL**: `EXISTS (SELECT 1 FROM item_selection cf WHERE ... AND cf.choice_id IN (5, 3))`

### **Test Case 3: Custom Text Field**
```bash
GET /servicetrack/item/list?filterConditions=["[{\"fieldId\":10002,\"operator\":\"$contains\",\"value\":\"urgent\"}]"]&projectId=101
```
**Expected SQL**: `EXISTS (SELECT 1 FROM item_text cf WHERE ... AND cf.text LIKE '%urgent%')`

### **Test Case 4: NOT Contains**
```bash
GET /servicetrack/item/list?filterConditions=["[{\"fieldId\":3,\"operator\":\"$notContains\",\"value\":[2,4]}]"]&projectId=101
```
**Expected SQL**: `(u.state_id NOT IN (2, 4) OR u.state_id IS NULL)`

## ✅ Resolution Status

- ✅ **System field array support** - Status, Owner, etc. support IN operations
- ✅ **Custom selection field support** - Dropdown, MultipleSelection, CheckBox, RadioBox
- ✅ **Array value parsing** - Multiple input formats supported
- ✅ **Smart operator interpretation** - LIKE for text, IN for selections
- ✅ **NULL value handling** - Proper NULL handling in NOT operations
- ✅ **Compilation fixes** - Resolved unreachable statement issues

## 🎉 Benefits

1. **Flexible Filtering**: Support for both single values and arrays
2. **Type-Aware Operations**: Different logic for different field types
3. **Comprehensive Coverage**: All major field types supported
4. **Consistent API**: Same operator syntax across field types
5. **Performance Optimized**: Efficient SQL generation for each case

The filtering functionality now fully supports complex selection field queries! 🚀

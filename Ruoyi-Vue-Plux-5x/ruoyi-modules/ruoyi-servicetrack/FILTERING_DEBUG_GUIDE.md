# Filtering Debug Guide

## 🔍 Current Issue Analysis

**Input**: `{"fieldId":1,"operator":"$eq","value":"oa"}`
**Generated SQL**: `u.item_title = 'oa'`
**Problem**: Query returns no results or unexpected results

## ✅ SQL Generation Verification

The filtering logic is working correctly:
- ✅ `fieldId: 1` → `u.item_title` (Title field)
- ✅ `operator: "$eq"` → `=` (Exact match)
- ✅ `value: "oa"` → `'oa'` (Properly escaped)

## 🔍 Debugging Steps

### **Step 1: Verify Data Exists**
Check if there are any records with title "oa":
```sql
SELECT item_id, item_title, project_id 
FROM item_info 
WHERE project_id = 101 
AND item_title = 'oa';
```

### **Step 2: Check for Case Sensitivity**
Check if there are records with different cases:
```sql
SELECT item_id, item_title, project_id 
FROM item_info 
WHERE project_id = 101 
AND LOWER(item_title) = 'oa';
```

### **Step 3: Check for Extra Spaces**
Check if there are records with spaces:
```sql
SELECT item_id, item_title, project_id, LENGTH(item_title) as title_length
FROM item_info 
WHERE project_id = 101 
AND TRIM(item_title) = 'oa';
```

### **Step 4: Check All Titles in Project**
See what titles actually exist:
```sql
SELECT DISTINCT item_title 
FROM item_info 
WHERE project_id = 101 
ORDER BY item_title;
```

### **Step 5: Test with LIKE Instead**
Try using contains operator instead:
```json
{"fieldId":1,"operator":"$contains","value":"oa"}
```
This generates: `u.item_title LIKE '%oa%'`

## 🧪 Alternative Test Cases

### **Test Case 1: Use Contains Instead of Equals**
```bash
GET /servicetrack/item/list?filterConditions=["[{\"fieldId\":1,\"operator\":\"$contains\",\"value\":\"oa\"}]"]&projectId=101
```

### **Test Case 2: Test with Known Data**
First, find an existing title:
```sql
SELECT item_title FROM item_info WHERE project_id = 101 LIMIT 1;
```

Then test with that exact title:
```bash
GET /servicetrack/item/list?filterConditions=["[{\"fieldId\":1,\"operator\":\"$eq\",\"value\":\"ACTUAL_TITLE_FROM_DB\"}]"]&projectId=101
```

### **Test Case 3: Test Other Fields**
Try filtering by a numeric field like status:
```bash
GET /servicetrack/item/list?filterConditions=["[{\"fieldId\":3,\"operator\":\"$eq\",\"value\":1}]"]&projectId=101
```

## 🔧 Troubleshooting Common Issues

### **Issue 1: No Results Found**
**Possible Causes**:
- No records match the exact criteria
- Case sensitivity differences
- Extra whitespace in data
- Wrong field ID or value

**Solutions**:
- Use `$contains` instead of `$eq`
- Check actual data in database
- Trim whitespace in data or query

### **Issue 2: Too Many Results**
**Possible Causes**:
- Filter not being applied
- Wrong operator logic
- SQL generation error

**Solutions**:
- Check generated SQL in logs
- Verify filter conditions are being processed
- Test with more specific criteria

### **Issue 3: SQL Errors**
**Possible Causes**:
- Special characters in value
- SQL injection attempts
- Invalid field IDs

**Solutions**:
- Check value escaping
- Validate field IDs
- Review error logs

## 📋 Verification Checklist

- [ ] **Data exists**: Confirmed records exist with the search criteria
- [ ] **Case matching**: Title case matches exactly
- [ ] **No extra spaces**: Data doesn't have leading/trailing spaces
- [ ] **Field ID correct**: Field ID 1 maps to title field
- [ ] **Operator correct**: `$eq` is appropriate for the search
- [ ] **Value format**: Value is in correct format (string, number, etc.)
- [ ] **Project ID**: Correct project ID is being used
- [ ] **Permissions**: User has access to view the records

## 🎯 Recommended Next Steps

1. **Run database queries** to verify data exists
2. **Try `$contains` operator** for more flexible matching
3. **Check application logs** for any error messages
4. **Test with known good data** to verify filtering works
5. **Use browser dev tools** to inspect the actual request/response

## 💡 Quick Fix Suggestions

### **For Flexible Matching**:
```json
{"fieldId":1,"operator":"$contains","value":"oa"}
```

### **For Case-Insensitive Search** (if needed):
Consider adding a case-insensitive operator or modify the existing logic.

### **For Debugging**:
Add logging to see what conditions are being generated:
```java
log.info("Generated filter condition: {}", filterCondition);
```

The filtering implementation is correct - the issue is likely with the data or search criteria! 🔍

# ItemInfo Filtering Functionality

## Overview
The ItemInfoServiceImpl now supports advanced filtering functionality through the `filterConditions` property in `ItemListBo`. This allows filtering items based on both system fields and custom fields using MongoDB-style operators.

## Supported Operators

| Operator | Label | Description | Supported Field Types |
|----------|-------|-------------|----------------------|
| `$eq` | 等于 | Equals | All field types |
| `$ne` | 不等于 | Not equals | All field types |
| `$lt` | 小于 | Less than | Date, Amount fields |
| `$lte` | 小于等于 | Less than or equal | Date, Amount fields |
| `$gt` | 大于 | Greater than | Date, Amount fields |
| `$gte` | 大于等于 | Greater than or equal | Date, Amount fields |
| `$contains` | 包含 | Contains (LIKE) | Text fields only |
| `$notContains` | 不包含 | Not contains (NOT LIKE) | Text fields only |

## Field Types

### System Fields
System fields are stored directly in the `item_info` table:

| Field ID | Field Name | Column Name | Type |
|----------|------------|-------------|------|
| 1 | Title | u.item_title | Text |
| 2 | Description | u.item_description | Text |
| 3 | Status | u.state_id | Number |
| 4 | Owner | u.owner_id | Number |
| 5 | Type | u.type_id | Number |
| 10 | IncidentID | u.item_id | Number |
| 18 | SubmittedTime | u.created_time | Date |
| 19 | LastModifiedTime | u.modified_time | Date |
| 20 | SubmittedBy | u.created_by | Number |
| 21 | LastModifiedBy | u.modified_by | Number |
| 22 | Customer | u.customer_id | Number |
| 23 | ClosedBy | u.closed_by | Number |
| 24 | ClosedTime | u.closed_time | Date |

### Custom Fields
Custom fields are stored in separate tables based on field type:

| Field Type | Table Name | Column Name | Description |
|------------|------------|-------------|-------------|
| 1, 2, 6 | item_text | text | Text fields (ShortText, PlainText, RichText) |
| 5 | item_datetime | datetime | Date fields |
| 3, 4, 7, 8 | item_selection | choice_id | Selection fields (Dropdown, MultipleSelection, CheckBox, RadioBox) |
| 11 | item_amount | amount | Amount fields |

## Usage Examples

### 1. Filter by System Field (Title contains "bug")
```json
{
  "projectId": 1,
  "fieldIds": [1, 2, 3],
  "filterConditions": [
    {
      "filterId": 1,
      "operator": "$contains",
      "value": "bug"
    }
  ]
}
```

### 2. Filter by System Field (Status equals specific state)
```json
{
  "projectId": 1,
  "fieldIds": [1, 2, 3],
  "filterConditions": [
    {
      "filterId": 3,
      "operator": "$eq",
      "value": 2
    }
  ]
}
```

### 3. Filter by Custom Text Field
```json
{
  "projectId": 1,
  "fieldIds": [1, 2, 10001],
  "filterConditions": [
    {
      "filterId": 10001,
      "operator": "$contains",
      "value": "urgent"
    }
  ]
}
```

### 4. Filter by Custom Date Field (Created after specific date)
```json
{
  "projectId": 1,
  "fieldIds": [1, 2, 10002],
  "filterConditions": [
    {
      "filterId": 10002,
      "operator": "$gt",
      "value": "2024-01-01"
    }
  ]
}
```

### 5. Multiple Filter Conditions (AND logic)
```json
{
  "projectId": 1,
  "fieldIds": [1, 2, 3, 10001],
  "filterConditions": [
    {
      "filterId": 1,
      "operator": "$contains",
      "value": "bug"
    },
    {
      "filterId": 3,
      "operator": "$eq",
      "value": 1
    },
    {
      "filterId": 10001,
      "operator": "$ne",
      "value": "closed"
    }
  ]
}
```

## Implementation Details

### System Field Filtering
System fields are filtered directly on the main query using MyBatis-Plus QueryWrapper conditions.

### Custom Field Filtering
Custom fields use EXISTS subqueries to filter based on values in the respective custom field tables:

```sql
EXISTS (
    SELECT 1 FROM item_text cf 
    WHERE cf.project_id = u.project_id 
    AND cf.item_id = u.item_id 
    AND cf.field_id = ? 
    AND cf.text LIKE ?
)
```

### Error Handling
- Invalid field IDs are skipped with warning logs
- Unsupported operator/field type combinations are ignored
- Null or empty filter values are skipped

### Performance Considerations
- Custom field filtering uses indexed EXISTS subqueries
- System field filtering uses direct column conditions
- All conditions use parameterized queries to prevent SQL injection

## Testing
To test the filtering functionality, use the existing item list API endpoint with the new `filterConditions` parameter in the request body.

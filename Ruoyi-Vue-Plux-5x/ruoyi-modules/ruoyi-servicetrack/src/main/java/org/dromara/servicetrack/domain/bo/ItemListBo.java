package org.dromara.servicetrack.domain.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.List;

/**
 * 条目列表业务对象 itemList
 *
 * <AUTHOR> fei
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ItemListBo  extends STBaseEntity {
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    @Min(value = 1, message = "projectId 必须大于 0")
    private Integer projectId;

    /**
     * 条目拥有者IDs
     */
    private List<Integer> ownerIds;

    /**
     * 条目状态ID
     */
    private List<Integer> stateIds;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 排序字段ID：positiveInt：升序；negativeInt：降序
     */
    private Integer sortFieldId;

    /**
     * 条目字段ID
     */
    @NotNull(message = "条目字段ID不能为空")
    List<Integer> fieldIds;

    @JsonIgnore
    private String sqlSegment;

    private List<FilterConditionInfo> filterConditions;
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class FilterConditionInfo extends STBaseEntity{
        private Integer fieldId;
        private String operator;
        private Object value;
    }
}

package org.dromara.servicetrack.domain.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 条目列表业务对象 itemList
 *
 * <AUTHOR> fei
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
public class ItemListBo  extends STBaseEntity {
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    @Min(value = 1, message = "projectId 必须大于 0")
    private Integer projectId;

    /**
     * 条目拥有者IDs
     */
    private List<Integer> ownerIds;

    /**
     * 条目状态ID
     */
    private List<Integer> stateIds;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 排序字段ID：positiveInt：升序；negativeInt：降序
     */
    private Integer sortFieldId;

    /**
     * 条目字段ID
     */
    @NotNull(message = "条目字段ID不能为空")
    List<Integer> fieldIds;

    @JsonIgnore
    private String sqlSegment;

    private List<FilterConditionInfo> filterConditions;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class FilterConditionInfo extends STBaseEntity{
        private Integer fieldId;
        private String operator;
        private Object value;

        // Getter for compatibility with existing code that uses filterId
        public Integer getFilterId() {
            return fieldId;
        }

        // Setter for compatibility with existing code that uses filterId
        public void setFilterId(Integer filterId) {
            this.fieldId = filterId;
        }
    }
}

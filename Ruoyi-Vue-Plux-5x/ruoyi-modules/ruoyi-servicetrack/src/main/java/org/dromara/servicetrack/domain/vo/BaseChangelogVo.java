package org.dromara.servicetrack.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;

import java.util.Date;
import java.util.List;

/**
 * changelog 基础视图对象 base changelog
 */
@Data
@EqualsAndHashCode(callSuper =true)
public class BaseChangelogVo extends STBaseEntity {
    /**
     * changelog Id
     */
    private Integer changelogId;

    /**
     * log_time
     */
    private Date logTime;

    /**
     * changedById
     */
    private Integer changedById;

    /**
     * changedByName
     */
    private String ChangedByUserName;

    /*
     * changedBy User Avatar Url
     */
    private String ChangedByUserAvatarUrl;
    /*
     * changelog Fields
     */
    private List<BaseChangelogFieldVo> changelogFields;

}

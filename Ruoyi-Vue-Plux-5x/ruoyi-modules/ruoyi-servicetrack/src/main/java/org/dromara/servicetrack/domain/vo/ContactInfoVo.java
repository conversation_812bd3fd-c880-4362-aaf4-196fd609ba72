package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.UserInfo;

import java.io.Serializable;

/**
 * 用户信息视图对象 sys_user info
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserInfo.class)
public class ContactInfoVo extends STUserInfoVo implements Serializable {
}

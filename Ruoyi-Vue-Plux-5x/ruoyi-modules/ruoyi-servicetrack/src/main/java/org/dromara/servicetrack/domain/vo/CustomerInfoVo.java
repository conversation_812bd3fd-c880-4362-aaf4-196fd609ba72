package org.dromara.servicetrack.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.xss.Xss;
import org.dromara.servicetrack.domain.CustomerInfo;
import org.dromara.servicetrack.model.field.TFieldValueVo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 客户信息视图对象 customer_info
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CustomerInfo.class)
public class CustomerInfoVo extends BaseInfoVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 客户ID
     */
    private Integer customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户状态
     */
    private String customerStatus;

    /**
     * 客户备注
     */
    private String customerNotes;
    /**
     * 创建者
     */
    private Integer createdBy;
    /**
     * 创建时间
     */

    private Date createdTime;

    /**
     * 修改者
     */
    private Integer modifiedBy;

    /**
     * 修改时间
     */
    private Date modifiedTime;

    private Integer customerType;
    private String customerTypeName;

    private Integer customerIndustry;

    private String customerIndustryName;

    private Integer customerLevel;
    private String customerLevelName;

    private String customerAddress;
    /**
     * 字段 values
     */
    private List<TFieldValueVo> fields;

    /**
     * 变更记录
     */
    private List<UserInfoChangelogVo> changelogs;
}

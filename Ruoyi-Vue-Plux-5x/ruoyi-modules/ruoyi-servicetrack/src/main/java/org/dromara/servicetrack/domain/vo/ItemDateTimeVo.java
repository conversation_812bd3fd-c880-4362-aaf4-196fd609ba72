package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemDateTime;

import java.util.Date;

/**
 *视图对象 item_datetime
 */
@Data
@EqualsAndHashCode(callSuper =true)
@AutoMapper(target = ItemDateTime.class)
public class ItemDateTimeVo extends ItemFieldVo{
    private Date dateTime;
}

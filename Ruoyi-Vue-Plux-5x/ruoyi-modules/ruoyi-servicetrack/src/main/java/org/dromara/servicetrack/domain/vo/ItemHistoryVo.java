package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.ItemHistory;
import org.dromara.servicetrack.domain.ItemInfo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 视图对象 item_history
 */
@Data
@AutoMapper(target = ItemHistory.class)
public class ItemHistoryVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

//    /**
//     * ID
//     */
//    private Long id;
//    /**
//     * project id
//     */
//    private Integer projectId;
//
//    /**
//     * item id
//     */
//    private Integer itemId;

    /**
     * seq no
     */
    private Integer seqNo;

    /**
     * datetime
     */
    private Date dateTime;

    /**
     * user_id
     */
    private Integer userId;
    /**
     * user_name
     */
    private String userName;

    /**
     * state_from
     */
    private Integer stateFrom;
    /*
    * state_from_name
     */
    private String stateFromName;
    /**
     * state_to
     */
    private Integer stateTo;

    /*
    * state_to_option_id
     */
    private Integer stateToOptionId;
    /*
    * state_to name
     */
    private String stateToName;

    /**
     * owner_from
     */
    private Integer ownerFrom;
    /*
     * owner_from name
     */
    private String ownerFromName;

    /**
     * owner_to
     */
    private Integer ownerTo;
    /*
    * owner_to name
     */
    private String ownerToName;

    /*
    * transition id
     */
    private Integer Transition;
    /*
      * transition name
     */
    private String TransitionName;
}

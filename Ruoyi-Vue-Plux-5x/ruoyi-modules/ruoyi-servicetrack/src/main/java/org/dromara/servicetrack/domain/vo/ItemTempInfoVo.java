package org.dromara.servicetrack.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.dromara.servicetrack.domain.ItemTempInfo;

import java.io.Serial;
import java.io.Serializable;

/**
 *视图对象 item_temp_group
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ItemTempInfo.class)
public class ItemTempInfoVo extends CustomFieldsVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * project id
     */
    @JsonIgnore
    private Integer projectId;
    /**
     * template id
     */
    private Integer templateId;
    /**
     * template Name
     */
    private String templateName;
    /**
     * template desc
     */
    private String templateDesc;
    /**
     * template type
     */
    private Integer templateType;
}

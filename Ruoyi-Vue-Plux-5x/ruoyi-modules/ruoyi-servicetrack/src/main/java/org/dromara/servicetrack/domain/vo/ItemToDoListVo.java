package org.dromara.servicetrack.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.STBaseEntity;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.servicetrack.domain.vo.ProjectFieldVo;

import java.io.Serializable;
import java.util.List;

@Data
public class ItemToDoListVo implements Serializable {

    /**
     * 项目 item todo list
     */
    private List<ProjectItemToDoListVo>  itemToDoList;
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ProjectItemToDoListVo extends STBaseEntity {
        private Integer projectId;
        private List<ProjectFieldVo> selectedFields ;
        private TableDataInfo<ItemListVo> ItemData;
    }
}

package org.dromara.servicetrack.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class TrendReportVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    public TrendReportVo(String trendTimePeriod, Integer itemCount) {
        this.trendTimePeriod = trendTimePeriod;
        this.itemCount = itemCount;
    }
    /**
     * trend 日期
     */
    @JsonIgnore
    private Date trendDate;

    /**
     * trend 日期
     */
    private String trendTimePeriod;
    /**
     * 条目数量
     */
    private Integer itemCount;
}

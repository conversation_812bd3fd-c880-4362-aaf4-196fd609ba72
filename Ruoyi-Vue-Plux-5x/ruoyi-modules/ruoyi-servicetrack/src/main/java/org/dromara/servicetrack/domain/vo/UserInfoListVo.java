package org.dromara.servicetrack.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户信息ListView视图对象 sys_user info
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
public class UserInfoListVo extends CustomFieldsVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    protected Long userId;

    /**
     * 用户账号
     */
    @JsonIgnore
    protected String userName;

    /**
     * 用户昵称
     */
    @JsonIgnore
    protected String nickName;

    /**
     * 部门ID
     */
    @JsonIgnore
    protected Long deptId;

    @JsonIgnore
    protected String deptName;
    /**
     * 用户邮箱
     */
    @JsonIgnore
    protected String email;

    /**
     * 手机号码
     */
    @JsonIgnore
    protected String phoneNumber;

    /**
     * 创建时间
     */
    @JsonIgnore
    protected Date createTime;

    /**
     * 创建者
     */
    @JsonIgnore
    protected Integer createBy;

    /**
     * 创建者名称
     */
    @JsonIgnore
    protected String createByName;
    /**
     * 用户状态（0正常 1停用）
     */
    @JsonIgnore
    protected Integer status;
    /**
     * 外部用户ID
     */
    @JsonIgnore
    protected Integer externalUserId;

    /**
     * service track 用户类型(refer to eSTModuleType): 1:EP, 2:SP, 3: EP & SP
     */
    protected Integer stUserType;

    /**
     * 是否为service track管理员
     */
    protected Integer  stAdmin;

    /**
     * User字段数据
     */
    protected List<ListFieldVo> values;
}

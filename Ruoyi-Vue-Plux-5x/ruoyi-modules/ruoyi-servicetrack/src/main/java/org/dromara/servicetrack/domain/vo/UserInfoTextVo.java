package org.dromara.servicetrack.domain.vo;


import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.UserInfoText;

/**
 *视图对象 user_info_text
 */
@Data
@EqualsAndHashCode(callSuper =true)
@AutoMapper(target = UserInfoText.class)
public class UserInfoTextVo extends UserInfoFieldVo{
    /**
     * 文本内容
     */
    private String text;
}

package org.dromara.servicetrack.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.servicetrack.domain.UserSetting;

import java.io.Serial;
import java.io.Serializable;

@Data
@AutoMapper(target = UserSetting.class)
public class UserSettingVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private Long id;
    /*
     *项目id
     */
    private Integer projectId;
    /*
     *用户id
     */
    private Integer userId;
    /*
     *设置id
     */
    private Integer settingId;
    /*
     *设置名称
     */
    private String settingName;
    /*
     *设置选项
     */
    private Integer settingOption;

    /*
     *设置内容
     */
    private String settingContent;
}

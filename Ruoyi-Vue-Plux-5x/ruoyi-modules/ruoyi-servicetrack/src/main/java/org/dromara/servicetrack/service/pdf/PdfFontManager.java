package org.dromara.servicetrack.service.pdf;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.ConcurrentHashMap;

/**
 * PDF Font Manager
 * Responsible for loading and managing Noto Sans CJK fonts (Regular and Bold)
 *
 * <AUTHOR> Team
 */
@Slf4j
@Component
public class PdfFontManager {

    private static final String REGULAR_FONT_PATH = "/fonts/NotoSansCJKsc-Regular.ttf";
    private static final String BOLD_FONT_PATH = "/fonts/NotoSansCJKsc-Bold.ttf";

    // Font cache to avoid repeated loading
    private final ConcurrentHashMap<String, byte[]> fontCache = new ConcurrentHashMap<>();

    /**
     * Font weight enumeration
     */
    @Getter
    public enum FontWeight {
        REGULAR(REGULAR_FONT_PATH),
        BOLD(BOLD_FONT_PATH);

        private final String path;

        FontWeight(String path) {
            this.path = path;
        }

    }

    /**
     * Load font
     *
     * @param document PDF document object
     * @param weight Font weight (Regular or Bold)
     * @return PDFont object
     * @throws IOException Thrown when font loading fails
     */
    public PDFont loadFont(PDDocument document, FontWeight weight) throws IOException {
        String fontPath = weight.getPath();

        // Get font data from cache
        byte[] fontData = fontCache.computeIfAbsent(fontPath, this::loadFontData);

        if (fontData == null) {
            log.warn("Unable to load font file: {}, using default font", fontPath);
            return new org.apache.pdfbox.pdmodel.font.PDType1Font(Standard14Fonts.FontName.HELVETICA);
        }

        return PDType0Font.load(document, new ByteArrayInputStream(fontData));
    }

    /**
     * Load font data from resource file
     *
     * @param fontPath Font file path
     * @return Font data byte array, returns null if loading fails
     */
    private byte[] loadFontData(String fontPath) {
        try (InputStream fontStream = getClass().getResourceAsStream(fontPath)) {
            if (fontStream == null) {
                log.error("Font file does not exist: {}", fontPath);
                return null;
            }
            return fontStream.readAllBytes();
        } catch (IOException e) {
            log.error("Failed to load font file: {}", fontPath, e);
            return null;
        }
    }

    /**
     * Clear font cache
     * Mainly used for testing or memory management
     */
    public void clearCache() {
        fontCache.clear();
        log.info("Font cache cleared");
    }

    /**
     * Get cache size
     *
     * @return Number of fonts in cache
     */
    public int getCacheSize() {
        return fontCache.size();
    }

    /**
     * Check if font file exists
     *
     * @param weight Font weight
     * @return true if font file exists, otherwise false
     */
    public boolean isFontAvailable(FontWeight weight) {
        try (InputStream fontStream = getClass().getResourceAsStream(weight.getPath())) {
            return fontStream != null;
        } catch (IOException e) {
            log.debug("Exception occurred while checking font file: {}", weight.getPath(), e);
            return false;
        }
    }
}

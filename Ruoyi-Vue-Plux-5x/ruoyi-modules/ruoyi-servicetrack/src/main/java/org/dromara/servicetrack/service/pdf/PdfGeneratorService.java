package org.dromara.servicetrack.service.pdf;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.servicetrack.constant.eFieldTypeDef;
import org.dromara.common.servicetrack.constant.eItemTypeActionDef;
import org.dromara.common.servicetrack.constant.eSystemFieldDef;
import org.dromara.common.servicetrack.constant.stConstant;
import org.dromara.common.servicetrack.domain.vo.ItemAttachmentVo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageActionVo;
import org.dromara.common.servicetrack.domain.vo.ProjectPageFieldVo;
import org.dromara.common.servicetrack.logic.helper.FieldIdHelper;
import org.dromara.common.servicetrack.logic.project.ProjectManager;
import org.dromara.common.servicetrack.mapper.ProjectPageActionMapper;
import org.dromara.common.servicetrack.utils.ValueConvert;
import org.dromara.servicetrack.domain.vo.ItemInfoVo;
import org.dromara.servicetrack.domain.vo.ItemTimestampVo;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PDF Generation Service
 * Responsible for generating PDF documents for Item details
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PdfGeneratorService {

    private final PdfFontManager fontManager;
    private final ProjectPageActionMapper projectPageActionMapper;;

    // PDF页面配置
    private static final float MARGIN = 50;
    private static final float LINE_HEIGHT = 20;
    private static final float TITLE_FONT_SIZE = 16;
    private static final float HEADER_FONT_SIZE = 14;
    private static final float CONTENT_FONT_SIZE = 12;
    private static final float PAGE_WIDTH = PDRectangle.A4.getWidth();
    private static final float PAGE_HEIGHT = PDRectangle.A4.getHeight();
    private static final float MIN_Y_POSITION = MARGIN + 50; // 页面底部最小位置，留出空间

    // PDF上下文类，用于管理多页生成
    private static class PdfContext {
        PDDocument document;
        PDFont regularFont;
        PDFont boldFont;
        PDPage currentPage;
        PDPageContentStream currentStream;
        float currentY;
        int pageNumber = 0;

        PdfContext(PDDocument document, PDFont regularFont, PDFont boldFont) {
            this.document = document;
            this.regularFont = regularFont;
            this.boldFont = boldFont;
        }

        void createNewPage() throws IOException {
            // 在关闭当前页面前添加页码
            if (currentStream != null) {
                addPageNumber();
                currentStream.close();
            }

            // 创建新页面
            currentPage = new PDPage(PDRectangle.A4);
            document.addPage(currentPage);
            currentStream = new PDPageContentStream(document, currentPage);
            currentY = PAGE_HEIGHT - MARGIN;
            pageNumber++;
        }

        void addPageNumber() throws IOException {
            if (currentStream != null) {
                currentStream.beginText();
                currentStream.setFont(regularFont, 10);
                currentStream.newLineAtOffset(PAGE_WIDTH - MARGIN - 50, MARGIN - 20);
                currentStream.showText("Page " + pageNumber);
                currentStream.endText();
            }
        }

        void close() throws IOException {
            if (currentStream != null) {
                addPageNumber(); // 为最后一页添加页码
                currentStream.close();
            }
        }

        boolean needNewPage() {
            return currentY < MIN_Y_POSITION;
        }

        boolean needNewPage(float requiredSpace) {
            return currentY - requiredSpace < MIN_Y_POSITION;
        }
    }
    public Map<Integer, List<ProjectPageFieldVo>>  getProjectPageFields(Integer projectId,int itemTypeId) {
        var pageActions = projectPageActionMapper.selectPageActionListByItemTypeId(projectId, itemTypeId);
        List<Integer> pageIds;
        if(pageActions != null && !pageActions.isEmpty()) {
            pageIds = pageActions.stream().filter(pageAction -> pageAction.getActionId().equals(eItemTypeActionDef.Detail.getValue()))
                .map(ProjectPageActionVo::getPageId).filter(pageId -> pageId > stConstant.DEFAULT_CUSTOM_PAGE_ID)
                .sorted()
                .distinct().toList();
        } else {
            pageIds = new ArrayList<>();
        }
        var pageFieldsInCache = ProjectManager.getInstance(projectId).getProjectPageFields();
        if( pageIds.isEmpty()){
            var defaultPageId = pageFieldsInCache.stream().map(ProjectPageFieldVo::getPageId).filter(pageId -> pageId > stConstant.DEFAULT_CUSTOM_PAGE_ID)
                .sorted()
                .distinct().findFirst().orElse(0);
        }

        List<ProjectPageFieldVo> pageFields = pageFieldsInCache.stream()
            .filter(pageField -> pageIds.contains(pageField.getPageId()))
            .toList();


        // 构建新的 list，每个 row 只保留一个父 field，其 childFields 为同 row 的所有 field
        Map<Integer, List<ProjectPageFieldVo>> pageGrouped = new HashMap<>();
        for(var pageId:pageIds){
            var onePageFields = pageFields.stream().filter(pageField -> pageId.equals(pageField.getPageId())).sorted(Comparator.comparing(ProjectPageFieldVo::getPageRow)).toList();
            var fieldsInRow = new ArrayList<ProjectPageFieldVo>();
            for(var field:onePageFields) {

                ProjectPageFieldVo matchedRow = fieldsInRow.stream().filter(rowField -> rowField.getPageRow().equals(field.getPageRow())).findFirst().orElse(null);
                if( matchedRow == null){
                    matchedRow = new ProjectPageFieldVo();
                    matchedRow.setPageRow(field.getPageRow());
                    matchedRow.setPageColumn(field.getPageColumn());
                    matchedRow.setChildFields(new ArrayList<>());
                    matchedRow.setPageId(field.getPageId());
                    matchedRow.setPageName(field.getPageName());
                    matchedRow.setFieldId(field.getFieldId());
                    matchedRow.setFieldName(field.getFieldName());
                    matchedRow.setFieldType(field.getFieldType());
                    matchedRow.setFieldSubtype(field.getFieldSubtype());
                    matchedRow.setDisplayOrder(field.getDisplayOrder());
                    matchedRow.setModuleId(field.getModuleId());
                    matchedRow.setFormula(field.getFormula());
                    fieldsInRow.add(matchedRow);
                }
                //check if the field is already in the child fields, if not, add it
                if( matchedRow.getChildFields().stream().noneMatch(childField -> childField.getPageRow().equals(field.getPageRow()) && childField.getPageColumn().equals(field.getPageColumn()))){
                    matchedRow.getChildFields().add(field);
                }
            }
            pageGrouped.put(pageId, fieldsInRow);
        }

        return pageGrouped;
    }
    /**
     * Generate Item detail PDF
     *
     * @param itemInfo Item detail data
     * @param outputStream Output stream
     * @throws IOException Thrown when PDF generation fails
     */
    public void generateItemPdf(ItemInfoVo itemInfo, OutputStream outputStream) throws IOException {
        long startTime = System.currentTimeMillis();
        log.info("Starting PDF generation for Item ID: {}, Project ID: {}",
                itemInfo.getItemId(), itemInfo.getProjectId());

        try (PDDocument document = new PDDocument()) {
            // 加载字体
            PDFont regularFont = fontManager.loadFont(document, PdfFontManager.FontWeight.REGULAR);
            PDFont boldFont = fontManager.loadFont(document, PdfFontManager.FontWeight.BOLD);

            // 创建PDF上下文
            PdfContext context = new PdfContext(document, regularFont, boldFont);
           boolean hasPageFields = false;
            try {
                // 创建第一页
                context.createNewPage();
                var pageFields = getProjectPageFields(itemInfo.getProjectId(),itemInfo.getTypeId());
                if(pageFields == null || pageFields.isEmpty()){
                    log.warn("No page fields found for project: {}, item id: {}, item type id: {}", itemInfo.getProjectId(), itemInfo.getItemId(), itemInfo.getTypeId());
                }
                else {
                    hasPageFields = true;

                    // 性能优化：创建字段值查找Map，避免重复Stream操作
                    Map<Integer, String> fieldValueMap = new HashMap<>();
                    if (itemInfo.getFields() != null) {
                        for (var field : itemInfo.getFields()) {
                            fieldValueMap.put(field.getFieldId(),
                                field.getValue() != null ? field.getValue() : "");
                        }
                    }

                    // 绘制PDF内容（支持多页）- 按页面 → 行 → 子字段的层次结构
                for (var pageEntry : pageFields.entrySet()) {
                    var pageRows = pageEntry.getValue();
                    if (pageRows == null || pageRows.isEmpty()) {
                        continue;
                    }

                    // 绘制页面标题
                    var pageTitle = pageRows.get(0).getPageName();
                    drawPageTitle(context, pageTitle);

                    // 遍历页面中的每一行
                    for (var row : pageRows) {
                        if (row.getChildFields() == null || row.getChildFields().isEmpty()) {
                            continue;
                        }

                        // 绘制行标题（显示行信息）
                        String rowDisplayName = getRowDisplayName(row);
                        if (StringUtils.isNotEmpty(rowDisplayName)) {
                            drawRowHeader(context, rowDisplayName);
                        }

                        // 使用Set避免重复渲染同一字段（解决自引用问题）
                        Set<Integer> renderedFieldIds = new HashSet<>();

                        // 收集当前行的所有字段信息，准备在一行内显示
                        List<String> rowFieldsDisplay = new ArrayList<>();

                        // 遍历行中的所有子字段
                        for (var childField : row.getChildFields()) {
                            // 避免重复渲染同一字段
                            if (renderedFieldIds.contains(childField.getFieldId())) {
                                continue;
                            }
                            renderedFieldIds.add(childField.getFieldId());

                            // 快速查找字段值（O(1)操作，而不是O(n)的Stream过滤）
                            var fieldValue = fieldValueMap.getOrDefault(childField.getFieldId(), "");

                            // 根据字段类型处理不同的渲染方式
                            Integer fieldTypeId = childField.getFieldType();
                            if (fieldTypeId == eFieldTypeDef.RichText.getValue() || fieldTypeId == eFieldTypeDef.PlainText.getValue()) {
                                if(FieldIdHelper.isTimestampField(itemInfo.getProjectId(),childField.getFieldId())){
                                    // 时间戳字段：每条记录一行显示
                                    drawTimestamps(context, itemInfo, childField.getFieldName());
                                    continue;
                                }
                                if (fieldTypeId == eFieldTypeDef.RichText.getValue()) {
                                    // 富文本字段：保持HTML格式，单独渲染以支持HTML显示
                                    fieldValue = ValueConvert.decodeBase64(fieldValue);
                                    drawRichTextField(context, childField.getFieldName(), fieldValue);
                                } else {
                                    // 普通文本字段：添加到行显示列表
                                    String displayText = formatFieldForRowDisplay(childField.getFieldName(), fieldValue);
                                    rowFieldsDisplay.add(displayText);
                                }
                            } else if(childField.getFieldId() == eSystemFieldDef.Attachment.getValue() ) {
                                // 附件字段：每条记录一行显示，支持超链接
                                drawAttachmentsWithLinks(context, itemInfo, childField.getFieldName());
                            }
                            else {
                                // 普通字段：添加到行显示列表
                                String displayText = formatFieldForRowDisplay(childField.getFieldName(), fieldValue);
                                rowFieldsDisplay.add(displayText);
                            }
                        }

                        // 渲染当前行的所有普通字段（在一行内显示，字段名使用粗体）
                        if (!rowFieldsDisplay.isEmpty()) {
                            drawRowFieldsWithBoldNames(context, rowFieldsDisplay);
                        }

                        // 行之间添加额外间距
                        addRowSpacing(context);
                    }

                    // 页面之间添加额外间距
                    addPageSpacing(context);
                }
              }
            } finally {
                context.close();
            }

            document.save(outputStream);

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            if(hasPageFields) {
                log.info("PDF generated successfully, Project ID: {}, Item ID: {}, Total pages: {}, Duration: {}ms",
                    itemInfo.getProjectId(), itemInfo.getItemId(), context.pageNumber, duration);

                // 性能警告
                if (duration > 5000) {
                    log.warn("PDF generation took longer than expected: {}ms for Item ID: {}",
                            duration, itemInfo.getItemId());
                }
            }

        }
    }

    /**
     * Draw timestamp records
     */
    private void drawTimestamps(PdfContext context, ItemInfoVo itemInfo, String fieldName) throws IOException {
        List<ItemTimestampVo> timestamps = itemInfo.getTimestamps();
        if (timestamps == null || timestamps.isEmpty()) {
            return;
        }

        // 绘制字段名（粗体，与其他字段名一致）
        if (context.needNewPage()) {
            context.createNewPage();
        }

        context.currentStream.beginText();
        context.currentStream.setFont(context.boldFont, CONTENT_FONT_SIZE);
        context.currentStream.newLineAtOffset(MARGIN + 20, context.currentY);
        context.currentStream.showText(fieldName + ":");
        context.currentStream.endText();
        context.currentY -= LINE_HEIGHT;

        // 绘制每条时间戳记录
        for (ItemTimestampVo timestamp : timestamps) {
            if (context.needNewPage()) {
                context.createNewPage();
            }

            String createdBy = StringUtils.isNotEmpty(timestamp.getCreatedByName()) ?
                             timestamp.getCreatedByName() : "";
            String createdTime = timestamp.getCreatedTime() != null ?
                             timestamp.getCreatedTime() : "";
            String timestampValue = timestamp.getContent() != null ?
                timestamp.getContent() : "";

            // 绘制创建者和时间
            context.currentStream.beginText();
            context.currentStream.setFont(context.regularFont, CONTENT_FONT_SIZE);
            context.currentStream.newLineAtOffset(MARGIN + 40, context.currentY);
            context.currentStream.showText( timestamp.getSeqNo() + ". " + createdBy + " - " + createdTime);
            context.currentStream.endText();
            context.currentY -= LINE_HEIGHT;

            // 绘制内容
            if (StringUtils.isNotEmpty(timestampValue)) {
                context.currentStream.beginText();
                context.currentStream.setFont(context.regularFont, CONTENT_FONT_SIZE);
                context.currentStream.newLineAtOffset(MARGIN + 40, context.currentY);
                context.currentStream.showText(timestampValue);
                context.currentStream.endText();
                context.currentY -= LINE_HEIGHT;
            }

            context.currentY -= LINE_HEIGHT / 2; // Record spacing
        }
    }

    /**
     * Draw section header with page break support
     */
    private void drawSectionHeader(PdfContext context, String title) throws IOException {
        // 检查是否需要新页面（标题需要额外空间）
        if (context.needNewPage(LINE_HEIGHT * 3)) {
            context.createNewPage();
        }

        context.currentStream.beginText();
        context.currentStream.setFont(context.boldFont, HEADER_FONT_SIZE);
        context.currentStream.newLineAtOffset(MARGIN, context.currentY);
        context.currentStream.showText(title);
        context.currentStream.endText();

        context.currentY -= LINE_HEIGHT * 1.5f;
    }

    /**
     * Draw sub header with page break support
     */
    private void drawSubHeader(PdfContext context, String title) throws IOException {
        // 检查是否需要新页面
        if (context.needNewPage(LINE_HEIGHT * 2)) {
            context.createNewPage();
        }

        context.currentStream.beginText();
        context.currentStream.setFont(context.boldFont, CONTENT_FONT_SIZE);
        context.currentStream.newLineAtOffset(MARGIN + 20, context.currentY);
        context.currentStream.showText(title);
        context.currentStream.endText();

        context.currentY -= LINE_HEIGHT;
    }

    /**
     * Draw field with page break support
     */
    private void drawField(PdfContext context, String label, String value) throws IOException {
        // 检查是否需要新页面
        if (context.needNewPage()) {
            context.createNewPage();
        }

        context.currentStream.beginText();
        context.currentStream.setFont(context.regularFont, CONTENT_FONT_SIZE);
        context.currentStream.newLineAtOffset(MARGIN + 40, context.currentY);

        if(!StringUtils.isEmpty(label)){
            label += ": ";
        }
        String text = label + (StringUtils.isNotEmpty(value) ? value : "None");
        // Handle long text (simple truncation, actual projects may need line wrapping)
        if (text.length() > 80) {
            text = text.substring(0, 77) + "...";
        }

        context.currentStream.showText(text);
        context.currentStream.endText();

        context.currentY -= LINE_HEIGHT;
    }

    /**
     * Draw field with text wrapping support (enhanced for rich content)
     */
    private void drawFieldWithWrapping(PdfContext context, String label, String value) throws IOException {
        if (StringUtils.isEmpty(value)) {
            drawChildField(context, label, "None");
            return;
        }

        // 首先绘制字段标签
        drawFieldLabel(context, label);

        // 处理多行内容（如富文本转换后的内容）
        String[] lines = value.split("\n");

        for (String line : lines) {
            if (StringUtils.isEmpty(line.trim())) {
                // 空行，添加间距
                context.currentY -= LINE_HEIGHT * 0.5f;
                continue;
            }

            // 处理每一行的文本换行
            drawWrappedLine(context, line.trim());
        }

        // 字段结束后添加间距
        context.currentY -= LINE_HEIGHT * 0.3f;
    }

    /**
     * Draw field label with proper formatting
     */
    private void drawFieldLabel(PdfContext context, String label) throws IOException {
        if (context.needNewPage()) {
            context.createNewPage();
        }

        context.currentStream.beginText();
        context.currentStream.setFont(context.boldFont, CONTENT_FONT_SIZE);
        context.currentStream.newLineAtOffset(MARGIN + 20, context.currentY); // 减少缩进
        context.currentStream.showText(label + ":");
        context.currentStream.endText();

        context.currentY -= LINE_HEIGHT;
    }

    /**
     * Draw a single line with wrapping if necessary
     */
    private void drawWrappedLine(PdfContext context, String text) throws IOException {
        // 计算每行可容纳的字符数（考虑缩进）
        int maxCharsPerLine = 65; // 减少以适应更深的缩进

        if (text.length() <= maxCharsPerLine) {
            // 短文本，直接绘制
            drawContentLine(context, text);
        } else {
            // 长文本，需要换行
            String[] words = text.split(" ");
            StringBuilder currentLine = new StringBuilder();

            for (String word : words) {
                if (currentLine.length() + word.length() + 1 <= maxCharsPerLine) {
                    if (currentLine.length() > 0) {
                        currentLine.append(" ");
                    }
                    currentLine.append(word);
                } else {
                    // 当前行已满，绘制并开始新行
                    if (currentLine.length() > 0) {
                        drawContentLine(context, currentLine.toString());
                        currentLine = new StringBuilder(word);
                    } else {
                        // 单个词太长，强制截断
                        drawContentLine(context, word.substring(0, Math.min(word.length(), maxCharsPerLine - 3)) + "...");
                    }
                }
            }

            // 绘制最后一行
            if (currentLine.length() > 0) {
                drawContentLine(context, currentLine.toString());
            }
        }
    }

    /**
     * Draw content line with deeper indentation
     */
    private void drawContentLine(PdfContext context, String text) throws IOException {
        if (context.needNewPage()) {
            context.createNewPage();
        }

        context.currentStream.beginText();
        context.currentStream.setFont(context.regularFont, CONTENT_FONT_SIZE);
        context.currentStream.newLineAtOffset(MARGIN + 40, context.currentY); // 适中的缩进用于内容
        context.currentStream.showText(text);
        context.currentStream.endText();

        context.currentY -= LINE_HEIGHT;
    }

    /**
     * Draw a single line of text
     */
    private void drawSingleLine(PdfContext context, String text) throws IOException {
        // 检查是否需要新页面
        if (context.needNewPage()) {
            context.createNewPage();
        }

        context.currentStream.beginText();
        context.currentStream.setFont(context.regularFont, CONTENT_FONT_SIZE);
        context.currentStream.newLineAtOffset(MARGIN + 40, context.currentY);
        context.currentStream.showText(text);
        context.currentStream.endText();

        context.currentY -= LINE_HEIGHT;
    }

    /**
     * Draw page title with enhanced formatting
     */
    private void drawPageTitle(PdfContext context, String pageTitle) throws IOException {
        if (StringUtils.isEmpty(pageTitle)) {
            return;
        }

        // 检查是否需要新页面（页面标题需要更多空间）
        if (context.needNewPage(LINE_HEIGHT * 4)) {
            context.createNewPage();
        }

        // 添加页面标题前的间距
        context.currentY -= LINE_HEIGHT;

        context.currentStream.beginText();
        context.currentStream.setFont(context.boldFont, HEADER_FONT_SIZE + 1); // 页面标题稍大
        context.currentStream.newLineAtOffset(MARGIN, context.currentY);
        context.currentStream.showText(pageTitle);
        context.currentStream.endText();

        context.currentY -= LINE_HEIGHT * 2;
    }

    /**
     * Draw row header to visually separate rows
     */
    private void drawRowHeader(PdfContext context, String rowName) throws IOException {
        // 检查是否需要新页面
        if (context.needNewPage(LINE_HEIGHT * 2)) {
            context.createNewPage();
        }

        // 添加行标题前的间距
        context.currentY -= LINE_HEIGHT * 0.5f;

        context.currentStream.beginText();
        context.currentStream.setFont(context.boldFont, CONTENT_FONT_SIZE + 2); // 行标题比字段名稍大
        context.currentStream.newLineAtOffset(MARGIN + 10, context.currentY);
        context.currentStream.showText("▶ " + rowName);
        context.currentStream.endText();

        context.currentY -= LINE_HEIGHT * 1.2f;
    }

    /**
     * Draw child field with proper indentation
     */
    private void drawChildField(PdfContext context, String fieldName, String fieldValue) throws IOException {
        // 检查是否需要新页面
        if (context.needNewPage()) {
            context.createNewPage();
        }

        context.currentStream.beginText();
        context.currentStream.setFont(context.regularFont, CONTENT_FONT_SIZE);
        context.currentStream.newLineAtOffset(MARGIN + 20, context.currentY); // 减少缩进，避免两边空白过多

        String text = fieldName + ": " + (StringUtils.isNotEmpty(fieldValue) ? fieldValue : "None");
        // Handle long text (simple truncation, actual projects may need line wrapping)
        if (text.length() > 75) { // 稍微减少字符数以适应缩进
            text = text.substring(0, 72) + "...";
        }

        context.currentStream.showText(text);
        context.currentStream.endText();

        context.currentY -= LINE_HEIGHT;
    }

    // 缓存编译后的正则表达式以提高性能
    private static final java.util.regex.Pattern HTML_TAG_PATTERN =
        java.util.regex.Pattern.compile("<[^>]+>");
    private static final java.util.regex.Pattern WHITESPACE_PATTERN =
        java.util.regex.Pattern.compile("\\s+");
    private static final java.util.regex.Pattern NEWLINE_PATTERN =
        java.util.regex.Pattern.compile("\n\\s*\n+");

    /**
     * Parse HTML content and convert to plain text with enhanced formatting (optimized)
     */
    private String parseHtmlContent(String htmlContent) {
        if (StringUtils.isEmpty(htmlContent)) {
            return "";
        }

        // 性能优化：使用StringBuilder和预编译的正则表达式
        StringBuilder sb = new StringBuilder(htmlContent);

        // 快速简单的HTML清理（避免复杂的正则表达式）
        String result = htmlContent
            // 基本换行处理
            .replace("<br>", "\n")
            .replace("<br/>", "\n")
            .replace("<br />", "\n")
            .replace("</p>", "\n")
            .replace("</div>", "\n")
            .replace("</li>", "\n")
            // 列表项
            .replace("<li>", "• ");

        // 移除所有HTML标签（使用预编译的正则表达式）
        result = HTML_TAG_PATTERN.matcher(result).replaceAll("");

        // 解码常见HTML实体
        result = result
            .replace("&nbsp;", " ")
            .replace("&lt;", "<")
            .replace("&gt;", ">")
            .replace("&amp;", "&")
            .replace("&quot;", "\"")
            .replace("&#39;", "'");

        // 清理空白字符（使用预编译的正则表达式）
        result = WHITESPACE_PATTERN.matcher(result).replaceAll(" ");
        result = NEWLINE_PATTERN.matcher(result).replaceAll("\n\n");

        return result.trim();
    }

    /**
     * Add spacing between rows
     */
    private void addRowSpacing(PdfContext context) {
        context.currentY -= LINE_HEIGHT * 0.8f;
    }

    /**
     * Add spacing between pages
     */
    private void addPageSpacing(PdfContext context) {
        context.currentY -= LINE_HEIGHT * 1.5f;
    }

    /**
     * Format file size
     */
    private String formatFileSize(Long size) {
        if (size == null || size <= 0) {
            return "0 B";
        }

        String[] units = {"B", "KB", "MB", "GB"};
        int unitIndex = 0;
        double fileSize = size.doubleValue();

        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }

        return String.format("%.1f %s", fileSize, units[unitIndex]);
    }

    /**
     * Get row display name for header
     */
    private String getRowDisplayName(ProjectPageFieldVo row) {
        // 优先使用rowName，如果没有则使用其他标识
//        if (StringUtils.isNotEmpty(row.getRowName())) {
//            return row.getRowName();
//        }

        // 如果没有rowName，可以使用pageRow作为标识
//        if (row.getPageRow() != null) {
//            return "Row " + row.getPageRow();
//        }

        return null; // 没有合适的显示名称
    }

    /**
     * Format field for row display (compact format)
     */
    private String formatFieldForRowDisplay(String fieldName, String fieldValue) {
        return formatFieldForRowDisplay(fieldName, fieldValue, false);
    }

    /**
     * Format field for row display with option to limit length
     */
    private String formatFieldForRowDisplay(String fieldName, String fieldValue, boolean limitLength) {
        // 格式化为紧凑显示：字段名: 值
        String value = StringUtils.isNotEmpty(fieldValue) ? fieldValue : "";

        // 对于富文本内容，需要更严格的长度限制，并清理换行符
        value = value.replaceAll("\n", " ").replaceAll("\\s+", " ").trim();

        // 只有当需要限制长度时才进行截断（比如一行有两个field时）
        if (limitLength && value.length() > 50) {
            value = value.substring(0, 47) + "...";
        }

        return fieldName + "&&##" + value;
    }

    /**
     * Draw row fields with web-style layout (field names aligned, values close to names)
     */
    private void drawRowFieldsWithBoldNames(PdfContext context, List<String> rowFieldsDisplay) throws IOException {
        if (rowFieldsDisplay.isEmpty()) {
            return;
        }

        // 检查是否需要新页面
        if (context.needNewPage()) {
            context.createNewPage();
        }

        // 使用表格式布局：将字段分成两列显示
        float leftColumnX = MARGIN + 20;
        float rightColumnX = PAGE_WIDTH / 2 + 20; // 右列起始位置

        for (int i = 0; i < rowFieldsDisplay.size(); i += 2) {
            // 检查是否需要新页面
            if (context.needNewPage()) {
                context.createNewPage();
            }

            // 绘制左列字段
            if (i < rowFieldsDisplay.size()) {
                // 当有两个字段时，限制字段值长度
                boolean hasRightField = (i + 1 < rowFieldsDisplay.size());
                drawSingleFieldWebStyle(context, rowFieldsDisplay.get(i), leftColumnX, hasRightField);
            }

            // 绘制右列字段
            if (i + 1 < rowFieldsDisplay.size()) {
                drawSingleFieldWebStyle(context, rowFieldsDisplay.get(i + 1), rightColumnX, true);
            }

            // 移动到下一行
            context.currentY -= LINE_HEIGHT;
        }
    }

    /**
     * Draw a single field in web style (field name: value)
     */
    private void drawSingleFieldWebStyle(PdfContext context, String fieldDisplay, float startX, boolean limitLength) throws IOException {
        String[] parts = fieldDisplay.split("&&##", 4);

        if (parts.length == 2) {
            String fieldName = parts[0];
            String fieldValue = parts[1];

            // 当需要限制长度时（两列布局），截断字段值
            if (limitLength && fieldValue.length() > 30) {
                fieldValue = fieldValue.substring(0, 27) + "...";
            }

            // 绘制字段名（粗体）
            context.currentStream.beginText();
            context.currentStream.setFont(context.boldFont, CONTENT_FONT_SIZE);
            context.currentStream.newLineAtOffset(startX, context.currentY);
            context.currentStream.showText(fieldName + ":");
            context.currentStream.endText();

            // 计算字段名实际宽度（使用PDFBox的字符串宽度计算）
            float fieldNameWidth;
            try {
                fieldNameWidth = context.boldFont.getStringWidth(fieldName + ": ") / 1000 * CONTENT_FONT_SIZE;
            } catch (Exception e) {
                // 如果计算失败，使用估算值
                fieldNameWidth = (fieldName.length() + 2) * 7;
            }

            // 绘制字段值（普通字体），紧跟在字段名后面
            float valueX = startX + fieldNameWidth + 10; // 小间距
            context.currentStream.beginText();
            context.currentStream.setFont(context.regularFont, CONTENT_FONT_SIZE);
            context.currentStream.newLineAtOffset(valueX, context.currentY);
            context.currentStream.showText(fieldValue);
            context.currentStream.endText();

        } else {
            // 如果分割失败，使用原来的方式显示
            context.currentStream.beginText();
            context.currentStream.setFont(context.regularFont, CONTENT_FONT_SIZE);
            context.currentStream.newLineAtOffset(startX, context.currentY);
            context.currentStream.showText(fieldDisplay);
            context.currentStream.endText();
        }
    }

    /**
     * Draw multiple fields in a single row
     */
    private void drawRowFields(PdfContext context, List<String> rowFieldsDisplay) throws IOException {
        if (rowFieldsDisplay.isEmpty()) {
            return;
        }

        // 检查是否需要新页面
        if (context.needNewPage()) {
            context.createNewPage();
        }

        // 将多个字段组合在一行显示，用四个空格分开
        String combinedText = String.join("    ", rowFieldsDisplay);

        // 如果组合文本太长，需要换行处理（调整长度限制以适应四个空格分隔符）
        if (combinedText.length() > 90) {
            // 分多行显示
            drawWrappedRowFields(context, rowFieldsDisplay);
        } else {
            // 单行显示
            context.currentStream.beginText();
            context.currentStream.setFont(context.regularFont, CONTENT_FONT_SIZE);
            context.currentStream.newLineAtOffset(MARGIN + 20, context.currentY); // 减少缩进
            context.currentStream.showText(combinedText);
            context.currentStream.endText();

            context.currentY -= LINE_HEIGHT;
        }
    }

    /**
     * Draw row fields with wrapping when too long
     */
    private void drawWrappedRowFields(PdfContext context, List<String> rowFieldsDisplay) throws IOException {
        StringBuilder currentLine = new StringBuilder();
        int maxLineLength = 85; // 每行最大字符数（调整以适应四个空格分隔符）

        for (int i = 0; i < rowFieldsDisplay.size(); i++) {
            String field = rowFieldsDisplay.get(i);
            String separator = (i > 0) ? "    " : ""; // 四个空格分隔

            // 检查添加当前字段是否会超出行长度
            if (currentLine.length() + separator.length() + field.length() <= maxLineLength) {
                currentLine.append(separator).append(field);
            } else {
                // 当前行已满，先输出当前行
                if (currentLine.length() > 0) {
                    drawSingleRowLine(context, currentLine.toString());
                    currentLine = new StringBuilder(field);
                } else {
                    // 单个字段太长，直接输出
                    drawSingleRowLine(context, field);
                }
            }
        }

        // 输出最后一行
        if (currentLine.length() > 0) {
            drawSingleRowLine(context, currentLine.toString());
        }
    }

    /**
     * Draw a single line of row fields
     */
    private void drawSingleRowLine(PdfContext context, String text) throws IOException {
        if (context.needNewPage()) {
            context.createNewPage();
        }

        context.currentStream.beginText();
        context.currentStream.setFont(context.regularFont, CONTENT_FONT_SIZE);
        context.currentStream.newLineAtOffset(MARGIN + 20, context.currentY); // 减少缩进
        context.currentStream.showText(text);
        context.currentStream.endText();

        context.currentY -= LINE_HEIGHT;
    }

    /**
     * Draw timestamps as row field (with normal field name format)
     */
    private void drawTimestampsAsRowField(PdfContext context, ItemInfoVo itemInfo, String fieldName, List<String> rowFieldsDisplay) {
        List<ItemTimestampVo> timestamps = itemInfo.getTimestamps();
        if (timestamps == null || timestamps.isEmpty()) {
            String displayText = formatFieldForRowDisplay(fieldName, "");
            rowFieldsDisplay.add(displayText);
            return;
        }

        // 将时间戳记录格式化为简短显示
        StringBuilder timestampSummary = new StringBuilder();
        int count = 0;
        for (ItemTimestampVo timestamp : timestamps) {
            if (count > 0) timestampSummary.append("; ");

            String createdBy = StringUtils.isNotEmpty(timestamp.getCreatedByName()) ?
                             timestamp.getCreatedByName() : "Unknown";
            String content = StringUtils.isNotEmpty(timestamp.getContent()) ?
                           timestamp.getContent() : "";

            // 简化显示：创建者 - 内容（限制长度）
            String entry = createdBy;
            if (StringUtils.isNotEmpty(content)) {
                if (content.length() > 20) {
                    content = content.substring(0, 17) + "...";
                }
                entry += " - " + content;
            }

            timestampSummary.append(entry);
            count++;

            // 限制显示的时间戳数量
            if (count >= 3) {
                if (timestamps.size() > 3) {
                    timestampSummary.append("...(").append(timestamps.size() - 3).append(" more)");
                }
                break;
            }
        }

        String displayText = formatFieldForRowDisplay(fieldName, timestampSummary.toString());
        rowFieldsDisplay.add(displayText);
    }

    /**
     * Draw attachments as row field (with normal field name format)
     */
    private void drawAttachmentsAsRowField(PdfContext context, ItemInfoVo itemInfo, String fieldName, List<String> rowFieldsDisplay) {
        List<ItemAttachmentVo> attachments = itemInfo.getAttachments();
        if (attachments == null || attachments.isEmpty()) {
            String displayText = formatFieldForRowDisplay(fieldName, "None");
            rowFieldsDisplay.add(displayText);
            return;
        }

        // 将附件列表格式化为简短显示
        StringBuilder attachmentSummary = new StringBuilder();
        int count = 0;
        for (ItemAttachmentVo attachment : attachments) {
            if (count > 0) attachmentSummary.append("; ");

            String fileName = StringUtils.isNotEmpty(attachment.getAttachmentName()) ?
                            attachment.getAttachmentName() : "Unknown file";

            // 限制文件名长度
            if (fileName.length() > 15) {
                fileName = fileName.substring(0, 12) + "...";
            }

            attachmentSummary.append(fileName);
            count++;

            // 限制显示的附件数量
            if (count >= 3) {
                if (attachments.size() > 3) {
                    attachmentSummary.append("...(").append(attachments.size() - 3).append(" more)");
                }
                break;
            }
        }

        String displayText = formatFieldForRowDisplay(fieldName, attachmentSummary.toString());
        rowFieldsDisplay.add(displayText);
    }

    /**
     * Draw rich text field with HTML support
     */
    private void drawRichTextField(PdfContext context, String fieldName, String htmlContent) throws IOException {
        // 检查是否需要新页面
        if (context.needNewPage()) {
            context.createNewPage();
        }

        // 绘制字段名（粗体，与其他字段名一致）
        context.currentStream.beginText();
        context.currentStream.setFont(context.boldFont, CONTENT_FONT_SIZE);
        context.currentStream.newLineAtOffset(MARGIN + 20, context.currentY);
        context.currentStream.showText(fieldName + ":");
        context.currentStream.endText();
        context.currentY -= LINE_HEIGHT;

        // 对于HTML内容，我们需要保持原始格式
        // 注意：PDF不直接支持HTML渲染，这里我们保留HTML标签作为文本显示
        // 在实际应用中，可能需要使用专门的HTML到PDF转换库
        if (StringUtils.isNotEmpty(htmlContent)) {
            // 将HTML内容按行分割显示
            //暂时还是用plain text
            htmlContent = parseHtmlContent(htmlContent);
            String[] lines = htmlContent.split("\n");
            for (String line : lines) {
                if (context.needNewPage()) {
                    context.createNewPage();
                }

                context.currentStream.beginText();
                context.currentStream.setFont(context.regularFont, CONTENT_FONT_SIZE);
                context.currentStream.newLineAtOffset(MARGIN + 40, context.currentY);

                // 限制每行长度
                if (line.length() > 80) {
                    line = line.substring(0, 77) + "...";
                }
                context.currentStream.showText(line);
                context.currentStream.endText();
                context.currentY -= LINE_HEIGHT;
            }
        }

        context.currentY -= LINE_HEIGHT / 2;
    }

    /**
     * Draw attachments with hyperlinks
     */
    private void drawAttachmentsWithLinks(PdfContext context, ItemInfoVo itemInfo, String fieldName) throws IOException {
        List<ItemAttachmentVo> attachments = itemInfo.getAttachments();
        if (attachments == null || attachments.isEmpty()) {
            return;
        }

        // 绘制字段名（粗体，与其他字段名一致）
        if (context.needNewPage()) {
            context.createNewPage();
        }

        context.currentStream.beginText();
        context.currentStream.setFont(context.boldFont, CONTENT_FONT_SIZE);
        context.currentStream.newLineAtOffset(MARGIN + 20, context.currentY);
        context.currentStream.showText(fieldName + ":");
        context.currentStream.endText();
        context.currentY -= LINE_HEIGHT;

        // 绘制每个附件记录，包含超链接
        for (ItemAttachmentVo attachment : attachments) {
            if (context.needNewPage()) {
                context.createNewPage();
            }

            String fileName = StringUtils.isNotEmpty(attachment.getAttachmentName()) ?
                            attachment.getAttachmentName() : "Unknown file";
            String uploadTime = attachment.getCreatedTime() != null ?
                              DateUtils.dateTime(attachment.getCreatedTime()) : "Unknown time";

            // 创建下载链接（这里假设有一个下载API）
            String downloadUrl = attachment.getUrl();

            // 绘制文件名作为链接（蓝色文本表示链接）
            context.currentStream.beginText();
            context.currentStream.setFont(context.regularFont, CONTENT_FONT_SIZE);
            context.currentStream.newLineAtOffset(MARGIN + 40, context.currentY);
            context.currentStream.showText(fileName + " (" + uploadTime + ")");
            context.currentStream.endText();

            // 添加链接注释（PDF超链接）
            try {
                // 创建链接区域
                float linkX = MARGIN + 40;
                float linkY = context.currentY - 2;
                float linkWidth = fileName.length() * 6; // 估算文本宽度
                float linkHeight = LINE_HEIGHT;

                // 注意：这里需要PDFBox的链接注释功能
                // 实际实现可能需要更复杂的代码来创建可点击的超链接

            } catch (Exception e) {
                // 如果创建链接失败，继续处理
                log.warn("Failed to create hyperlink for attachment: {}", fileName);
            }

            context.currentY -= LINE_HEIGHT;
            context.currentY -= LINE_HEIGHT / 2; // Attachment spacing
        }
    }
}

# PDF字体文件说明

本目录用于存放PDF生成所需的中文字体文件。

## 所需字体文件

请下载以下Google Noto Sans CJK字体文件并放置在此目录下：

### 1. Noto Sans CJK SC Regular
- **文件名**: `NotoSansCJKsc-Regular.ttf`
- **用途**: PDF中的普通文本显示
- **下载地址**: https://fonts.google.com/noto/specimen/Noto+Sans+SC

### 2. Noto Sans CJK SC Bold
- **文件名**: `NotoSansCJKsc-Bold.ttf`
- **用途**: PDF中的粗体文本显示（标题、章节标题等）
- **下载地址**: https://fonts.google.com/noto/specimen/Noto+Sans+SC

## 下载步骤

1. 访问 Google Fonts Noto Sans SC 页面
2. 点击 "Download family" 按钮下载字体包
3. 解压下载的zip文件
4. 找到以下两个文件：
   - `NotoSansCJKsc-Regular.ttf`
   - `NotoSansCJKsc-Bold.ttf`
5. 将这两个文件复制到当前目录（`src/main/resources/fonts/`）

## 替代下载方式

如果无法访问Google Fonts，可以从以下地址下载：

- **GitHub仓库**: https://github.com/googlefonts/noto-cjk
- **直接下载链接**:
  - Regular: https://github.com/googlefonts/noto-cjk/raw/main/Sans/TTF/NotoSansCJKsc-Regular.ttf
  - Bold: https://github.com/googlefonts/noto-cjk/raw/main/Sans/TTF/NotoSansCJKsc-Bold.ttf

## 字体许可证

Google Noto Sans CJK字体使用SIL Open Font License，完全免费且可用于商业用途。

## 文件结构

下载完成后，目录结构应该如下：

```
fonts/
├── README.md                    (本说明文件)
├── NotoSansCJKsc-Regular.ttf   (普通字体)
└── NotoSansCJKsc-Bold.ttf      (粗体字体)
```

## 注意事项

1. **文件大小**: 每个字体文件约15-20MB，请确保有足够的存储空间
2. **文件名**: 请确保文件名完全匹配，区分大小写
3. **字体格式**: 必须使用.ttf格式的字体文件（Apache PDFBox不支持CFF轮廓的.otf文件）
4. **版本兼容**: 建议使用最新版本的Noto Sans CJK字体

## 故障排除

如果PDF生成时出现中文显示问题：

1. 检查字体文件是否存在于正确位置
2. 确认文件名是否正确（区分大小写）
3. 验证字体文件是否完整（未损坏）
4. 查看应用日志中的字体加载错误信息

## 技术支持

如有问题，请联系开发团队或查看项目文档。
